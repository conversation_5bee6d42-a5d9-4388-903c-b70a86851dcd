import { NestFactory } from '@nestjs/core';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';:fs';
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  const config = new DocumentBuilder()
    .setTitle('Danoh CrowBridge')
    .setDescription('This is the bridge between humans, called CrowBridge')
    .setVersion('1.0')
    .addTag('danoh')
    .addTag('crowbridge')
    .build();
  const documentFactory = () => SwaggerModule.createDocument(app, config);
  // Generating JSON spec file
  // writeFileSync('openapi.json', JSON.stringify(documentFactory));
  SwaggerModule.setup('api', app, documentFactory);

  await app.listen(process.env.PORT ?? 3000);
}

// biome-ignore lint/nursery/noFloatingPromises:false
// biome-ignore-start
// eslint-disable-next-line @typescript-eslint/no-floating-promises
bootstrap();
// biome-ignore-end
